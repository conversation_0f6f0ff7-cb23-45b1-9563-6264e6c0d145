# Enhanced Grid Comparison Plotter Usage Guide

## Overview

The `grid_comparison_plotter.py` script has been enhanced with command-line argument support, making it flexible for analyzing different TC Flash PQ log files while maintaining full backward compatibility.

## Usage Examples

### Basic Usage (Backward Compatible)
```bash
# Use default log file (TC_Flash_first_few_frames_pq_log.txt)
python grid_comparison_plotter.py
```

### Custom Log File
```bash
# Specify a different log file
python grid_comparison_plotter.py my_custom_log.txt

# Use full path to log file
python grid_comparison_plotter.py /path/to/tc_flash_logs/session_001.txt
```

### Custom Output Directory
```bash
# Specify custom output directory
python grid_comparison_plotter.py --output-dir custom_plots

# Combine custom log file and output directory
python grid_comparison_plotter.py my_log.txt --output-dir results/session_1
```

### Help and Documentation
```bash
# Display help information
python grid_comparison_plotter.py --help
```

## Command-Line Arguments

### Positional Arguments
- **`log_file`** (optional): Path to the TC Flash PQ log file
  - Default: `TC_Flash_first_few_frames_pq_log.txt`
  - Accepts relative or absolute paths

### Optional Arguments
- **`--output-dir`**: Output directory for generated plots
  - Default: `plots`
  - Directory will be created if it doesn't exist
- **`--help`**: Show help message and exit

## Log File Requirements

### Expected Format
The log file must contain TC Flash PQ data with the following structure:

```
fallback: 0
Config values:
  intensity_level:                         10
  PrecisionRenderingStrength:              24576
  DBrightness:                             9830
  ...

DM values:
  LmOn                                      1 (scale  0)
  TMax                                      208404480 (scale  0)
  LocalMappingStrength                      16319 (scale  0)
  ...
```

### Validation Checks
The script automatically validates:
1. **File Existence**: Checks if the specified file exists
2. **Required Sections**: Verifies presence of:
   - `"fallback: 0"` frame markers
   - `"Config values:"` sections
   - `"DM values:"` sections
3. **File Readability**: Ensures the file can be opened and read

## Error Handling

### File Not Found
```bash
$ python grid_comparison_plotter.py missing_file.txt
Error: Log file 'missing_file.txt' not found!
Please check the file path and try again.
```

### Invalid Log Format
```bash
$ python grid_comparison_plotter.py invalid_file.txt
Error: 'invalid_file.txt' does not appear to be a valid TC Flash PQ log file!
Expected sections not found:
  - Missing 'Config values:' section
  - Missing 'DM values:' section
  - Missing 'fallback: 0' frame markers
```

### Parsing Errors
```bash
Error parsing log file: [specific error details]
Please ensure the log file is in the correct TC Flash PQ format.
```

## Output Files

### Generated Plots
- **Main Output**: `all_parameters_comparison_grid.png`
  - High-resolution (500 DPI) comprehensive grid plot
  - Contains all parameter comparisons in a single view

### Output Location
- Default: `plots/all_parameters_comparison_grid.png`
- Custom: `{output_dir}/all_parameters_comparison_grid.png`

## Features Maintained

### Parameter Comparisons
- **6 Direct Comparisons**: Config vs DM parameter pairs
- **Config-Only Parameters**: Parameters unique to Config section
- **DM-Only Parameters**: Parameters unique to DM section

### Visual Elements
- Blue solid lines with circles: Config values
- Red dashed lines with squares: DM values
- Dotted trend lines: For varying parameters
- Value ranges: Displayed on each subplot
- Master legend: Consistent interpretation guide

## Backward Compatibility

The enhanced script maintains full backward compatibility:
- Running without arguments works exactly as before
- All existing functionality is preserved
- Default behavior unchanged
- Output format and quality identical

## Integration with Other Scripts

The enhanced script works seamlessly with:
- `log_parser_plotter.py`: Uses the same parsing functions
- `comparison_plotter.py`: Complementary individual comparison plots
- Existing workflow and analysis pipelines

## Best Practices

### File Organization
```
project/
├── grid_comparison_plotter.py
├── log_parser_plotter.py
├── logs/
│   ├── session_001.txt
│   ├── session_002.txt
│   └── baseline.txt
└── results/
    ├── session_001/
    ├── session_002/
    └── baseline/
```

### Batch Processing Example
```bash
# Process multiple log files
for log_file in logs/*.txt; do
    basename=$(basename "$log_file" .txt)
    python grid_comparison_plotter.py "$log_file" --output-dir "results/$basename"
done
```

## Troubleshooting

### Common Issues
1. **Permission Errors**: Ensure read access to log file and write access to output directory
2. **Path Issues**: Use absolute paths if relative paths don't work
3. **Memory Issues**: Large log files may require more memory

### Debug Tips
- Use `--help` to verify argument syntax
- Check file permissions with `ls -la`
- Verify log file format manually before processing

This enhanced version provides greater flexibility while maintaining the robust analysis capabilities of the original script.
