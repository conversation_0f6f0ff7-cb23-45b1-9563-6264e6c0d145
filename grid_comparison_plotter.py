#!/usr/bin/env python3
"""
Grid Comparison Plotter for TC Flash PQ Log Analysis

This script creates a comprehensive grid layout comparison plot that displays 
all parameter comparisons in a single image file.
"""

import re
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict
import os
import math
from log_parser_plotter import parse_log_file

def identify_all_parameters(config_data, dm_data):
    """
    Identify all parameters and their relationships for grid display.
    
    Returns:
        list: List of plot specifications for the grid
    """
    plot_specs = []
    
    # Define parameter pairs for comparison
    parameter_pairs = [
        ('DBrightness', 'dBrightness', 'Brightness Comparison'),
        ('DContrast', 'dContrast', 'Contrast Comparison'),
        ('DSaturation', 'dSaturation', 'Saturation Comparison'),
        ('DLocalContrast', 'dLocalContrast', 'Local Contrast Comparison'),
        ('DBrightness_PR_on', 'dBrightnessPRon', 'Brightness PR Comparison'),
    ]
    
    # Add comparison pairs
    for config_param, dm_param, title in parameter_pairs:
        if config_param in config_data and dm_param in dm_data:
            plot_specs.append({
                'type': 'comparison',
                'title': title,
                'config_param': config_param,
                'dm_param': dm_param,
                'config_values': config_data[config_param],
                'dm_values': dm_data[dm_param]
            })
    
    # Track which parameters are already used in comparisons
    used_config = {spec['config_param'] for spec in plot_specs if spec['type'] == 'comparison'}
    used_dm = {spec['dm_param'] for spec in plot_specs if spec['type'] == 'comparison'}
    
    # Add Config-only parameters (prioritize varying parameters)
    config_only = [(param, values) for param, values in config_data.items() 
                   if param not in used_config]
    config_only.sort(key=lambda x: len(set(x[1])), reverse=True)  # Varying parameters first
    
    for param, values in config_only:
        plot_specs.append({
            'type': 'config_only',
            'title': f'Config: {param}',
            'config_param': param,
            'dm_param': None,
            'config_values': values,
            'dm_values': None
        })
    
    # Add DM-only parameters (prioritize varying parameters)
    dm_only = [(param, values) for param, values in dm_data.items() 
               if param not in used_dm]
    dm_only.sort(key=lambda x: len(set(x[1])), reverse=True)  # Varying parameters first
    
    for param, values in dm_only:
        plot_specs.append({
            'type': 'dm_only',
            'title': f'DM: {param}',
            'config_param': None,
            'dm_param': param,
            'config_values': None,
            'dm_values': values
        })
    
    return plot_specs

def calculate_grid_layout(n_plots):
    """
    Calculate optimal grid layout for given number of plots.
    
    Returns:
        tuple: (n_rows, n_cols)
    """
    if n_plots <= 4:
        return (2, 2)
    elif n_plots <= 6:
        return (2, 3)
    elif n_plots <= 9:
        return (3, 3)
    elif n_plots <= 12:
        return (3, 4)
    elif n_plots <= 16:
        return (4, 4)
    elif n_plots <= 20:
        return (4, 5)
    else:
        # For larger numbers, aim for roughly square layout
        n_cols = math.ceil(math.sqrt(n_plots))
        n_rows = math.ceil(n_plots / n_cols)
        return (n_rows, n_cols)

def create_grid_comparison_plot(config_data, dm_data, output_dir="plots"):
    """
    Create a comprehensive grid layout comparison plot.
    """
    # Get all plot specifications
    plot_specs = identify_all_parameters(config_data, dm_data)
    n_plots = len(plot_specs)
    
    print(f"Creating grid comparison plot with {n_plots} subplots...")
    
    # Calculate grid layout
    n_rows, n_cols = calculate_grid_layout(n_plots)
    print(f"Using {n_rows}x{n_cols} grid layout")
    
    # Create the figure with appropriate size
    fig_width = max(20, n_cols * 4)
    fig_height = max(12, n_rows * 3)
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height))
    fig.suptitle('Comprehensive Parameter Comparison: Config vs DM Values', 
                 fontsize=20, fontweight='bold', y=0.98)
    
    # Handle different subplot configurations
    if n_rows == 1 and n_cols == 1:
        axes = [axes]
    elif n_rows == 1 or n_cols == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()
    
    # Get frame numbers (assuming all parameters have same length)
    frames = list(range(1, len(next(iter(config_data.values()))) + 1))
    
    # Create each subplot
    for i, spec in enumerate(plot_specs):
        ax = axes[i]
        
        if spec['type'] == 'comparison':
            # Plot both Config and DM values
            ax.plot(frames, spec['config_values'], 
                   color='blue', linewidth=2, marker='o', markersize=2,
                   label='Config', alpha=0.8)
            ax.plot(frames, spec['dm_values'],
                   color='red', linewidth=2, marker='s', markersize=2,
                   linestyle='--', label='DM', alpha=0.8)
            
        elif spec['type'] == 'config_only':
            # Plot only Config values
            ax.plot(frames, spec['config_values'],
                   color='blue', linewidth=2, marker='o', markersize=2,
                   label='Config', alpha=0.8)
            
        elif spec['type'] == 'dm_only':
            # Plot only DM values
            ax.plot(frames, spec['dm_values'],
                   color='red', linewidth=2, marker='s', markersize=2,
                   linestyle='--', label='DM', alpha=0.8)
        
        # Format subplot
        ax.set_title(spec['title'], fontsize=11, fontweight='bold', pad=10)
        ax.set_xlabel('Frame', fontsize=9)
        ax.set_ylabel('Value', fontsize=9)
        ax.grid(True, alpha=0.3)
        ax.tick_params(axis='both', which='major', labelsize=8)
        
        # Add trend lines for varying parameters
        if spec['type'] == 'comparison':
            config_varies = len(set(spec['config_values'])) > 1
            dm_varies = len(set(spec['dm_values'])) > 1
            
            if config_varies:
                z = np.polyfit(frames, spec['config_values'], 1)
                p = np.poly1d(z)
                ax.plot(frames, p(frames), color='blue', linestyle=':', alpha=0.5, linewidth=1)
            
            if dm_varies:
                z = np.polyfit(frames, spec['dm_values'], 1)
                p = np.poly1d(z)
                ax.plot(frames, p(frames), color='red', linestyle=':', alpha=0.5, linewidth=1)
                
        else:
            # Single parameter trend line
            values = spec['config_values'] if spec['config_values'] is not None else spec['dm_values']
            if len(set(values)) > 1:
                color = 'blue' if spec['type'] == 'config_only' else 'red'
                z = np.polyfit(frames, values, 1)
                p = np.poly1d(z)
                ax.plot(frames, p(frames), color=color, linestyle=':', alpha=0.5, linewidth=1)
        
        # Add value range as text
        if spec['type'] == 'comparison':
            config_range = f"[{min(spec['config_values'])}-{max(spec['config_values'])}]"
            dm_range = f"[{min(spec['dm_values'])}-{max(spec['dm_values'])}]"
            range_text = f"Config: {config_range}\nDM: {dm_range}"
        else:
            values = spec['config_values'] if spec['config_values'] is not None else spec['dm_values']
            value_range = f"[{min(values)}-{max(values)}]"
            section = "Config" if spec['type'] == 'config_only' else "DM"
            range_text = f"{section}: {value_range}"
        
        ax.text(0.02, 0.98, range_text, transform=ax.transAxes, fontsize=7,
                verticalalignment='top', bbox=dict(boxstyle='round,pad=0.3', 
                facecolor='white', alpha=0.8))
    
    # Hide unused subplots
    for i in range(n_plots, len(axes)):
        axes[i].set_visible(False)
    
    # Create master legend
    legend_elements = [
        plt.Line2D([0], [0], color='blue', linewidth=2, marker='o', markersize=4,
                   label='Config Values (solid line, circles)'),
        plt.Line2D([0], [0], color='red', linewidth=2, linestyle='--', marker='s', markersize=4,
                   label='DM Values (dashed line, squares)'),
        plt.Line2D([0], [0], color='gray', linewidth=1, linestyle=':', alpha=0.7,
                   label='Trend Lines (dotted)')
    ]
    
    fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.02),
               ncol=3, fontsize=12, frameon=True, fancybox=True, shadow=True)
    
    # Adjust layout to prevent overlap
    plt.tight_layout(rect=[0, 0.05, 1, 0.96])
    
    # Save the plot
    os.makedirs(output_dir, exist_ok=True)
    filepath = os.path.join(output_dir, "all_parameters_comparison_grid.png")
    plt.savefig(filepath, dpi=500, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"Saved comprehensive grid plot: all_parameters_comparison_grid.png")
    
    return n_plots

def print_grid_summary(config_data, dm_data):
    """
    Print a summary of what will be included in the grid plot.
    """
    plot_specs = identify_all_parameters(config_data, dm_data)
    
    print("\n" + "="*60)
    print("GRID PLOT SUMMARY")
    print("="*60)
    
    comparisons = [spec for spec in plot_specs if spec['type'] == 'comparison']
    config_only = [spec for spec in plot_specs if spec['type'] == 'config_only']
    dm_only = [spec for spec in plot_specs if spec['type'] == 'dm_only']
    
    print(f"\nParameter Comparisons ({len(comparisons)}):")
    for spec in comparisons:
        config_varies = len(set(spec['config_values'])) > 1
        dm_varies = len(set(spec['dm_values'])) > 1
        print(f"  {spec['title']}: Config {'varies' if config_varies else 'constant'}, "
              f"DM {'varies' if dm_varies else 'constant'}")
    
    print(f"\nConfig-Only Parameters ({len(config_only)}):")
    for spec in config_only:
        varies = len(set(spec['config_values'])) > 1
        print(f"  {spec['config_param']}: {'varies' if varies else 'constant'}")
    
    print(f"\nDM-Only Parameters ({len(dm_only)}):")
    for spec in dm_only:
        varies = len(set(spec['dm_values'])) > 1
        print(f"  {spec['dm_param']}: {'varies' if varies else 'constant'}")
    
    n_rows, n_cols = calculate_grid_layout(len(plot_specs))
    print(f"\nGrid Layout: {n_rows} rows × {n_cols} columns = {n_rows * n_cols} total positions")
    print(f"Used positions: {len(plot_specs)}")

def main():
    """
    Main function to create the grid comparison plot.
    """
    log_filename = "TC_Flash_first_few_frames_pq_log.txt"
    
    print("TC Flash PQ Log Grid Comparison Plotter")
    print("="*50)
    
    # Check if log file exists
    if not os.path.exists(log_filename):
        print(f"Error: Log file '{log_filename}' not found!")
        return
    
    print(f"Parsing log file: {log_filename}")
    
    # Parse the log file
    config_data, dm_data = parse_log_file(log_filename)
    
    # Print summary of what will be plotted
    print_grid_summary(config_data, dm_data)
    
    # Create the grid comparison plot
    n_plots = create_grid_comparison_plot(config_data, dm_data)
    
    print(f"\nGrid comparison plotting complete!")
    print(f"Created comprehensive visualization with {n_plots} parameter plots.")
    print(f"Output file: all_parameters_comparison_grid.png")
    
    print(f"\n" + "="*50)
    print("VISUALIZATION FEATURES")
    print("="*50)
    print("✓ All parameter comparisons in single view")
    print("✓ Blue solid lines: Config values")
    print("✓ Red dashed lines: DM values")
    print("✓ Trend lines for varying parameters")
    print("✓ Value ranges displayed on each subplot")
    print("✓ Optimized grid layout")
    print("✓ Master legend for consistent interpretation")

if __name__ == "__main__":
    main()
