# Comprehensive Grid Comparison Plot

## Overview

The `all_parameters_comparison_grid.png` file contains a comprehensive visualization of all TC Flash PQ log parameters in a single 4×5 grid layout. This visualization allows for easy comparison of corresponding parameters between Config and DM sections, as well as visualization of parameters that exist in only one section.

## Grid Layout Structure

**Total Subplots**: 17 parameters displayed in a 4×5 grid (20 positions, 3 unused)

### Parameter Categories

#### 1. Direct Comparisons (6 subplots)
These subplots overlay corresponding parameters from both Config and DM sections using correct parameter mappings:

- **Precision/Local Mapping Strength**: `PrecisionRenderingStrength` (Config) vs `LocalMappingStrength` (DM)
  - Config: Constant at 24,576
  - DM: Varies from 16,319 → 5,397 (decreasing trend)
  - **Analysis**: DM values decreasing significantly while Config remains constant

- **Brightness Comparison**: `DBrightness` (Config) vs `dBrightness` (DM)
  - Config: Constant at 9,830
  - DM: Constant at 9,830
  - **Analysis**: Perfect match between Config and DM values

- **Brightness PR Comparison**: `DBrightness_PR_on` (Config) vs `dBrightnessPRon` (DM)
  - Config: Constant at 11,469
  - DM: Varies from 16,319 → 5,397 (decreasing trend)
  - **Analysis**: DM values decreasing but not converging to Config level

- **Contrast Comparison**: `DContrast` (Config) vs `dContrast` (DM)
  - Config: Constant at 3,277
  - DM: Varies from 8,224 → 2,719 (decreasing trend)
  - **Analysis**: DM values start higher but decrease toward Config level

- **Saturation Comparison**: `DSaturation` (Config) vs `dSaturation` (DM)
  - Config: Constant at 3,277
  - DM: Varies from 8,224 → 2,719 (decreasing trend)
  - **Analysis**: Identical pattern to Contrast - DM converging to Config

- **Local Contrast Comparison**: `DLocalContrast` (Config) vs `dLocalContrast` (DM)
  - Config: Constant at 4,915
  - DM: Varies from 16,319 → 5,397 (decreasing trend)
  - **Analysis**: DM values decreasing toward Config level

#### 2. Config-Only Parameters (8 subplots)
Parameters that exist only in the Config section (after proper mapping):

**Varying Parameters** (7):
- `gain_PrecisionRenderingStrength`: 127 → 42 (decreasing)
- `gain_DLocalContrast`: 127 → 42 (decreasing)
- `gain_DBrightness`: 127 → 42 (decreasing)
- `gainPos_DContrast`: 127 → 42 (decreasing)
- `gainPos_DSaturation`: 127 → 42 (decreasing)
- `gainNeg_DContrast`: 127 → 42 (decreasing)
- `gainNeg_DSaturation`: 127 → 42 (decreasing)

**Constant Parameters** (1):
- `intensity_level`: Constant at 10

#### 3. DM-Only Parameters (3 subplots)
Parameters that exist only in the DM section (after proper mapping):

**Varying Parameters** (1):
- `UpMappingStrength`: 32,960 → 48,676 (increasing trend)

**Constant Parameters** (2):
- `LmOn`: Constant at 1
- `TMax`: Constant at 208,404,480

## Visual Coding System

### Line Styles and Colors
- **Blue Solid Lines with Circles**: Config values
- **Red Dashed Lines with Squares**: DM values
- **Dotted Lines**: Trend lines for varying parameters

### Information Display
- **Subplot Titles**: Clear parameter identification
- **Value Ranges**: Displayed in text boxes on each subplot
- **Trend Lines**: Automatically added for parameters that vary
- **Master Legend**: Consistent interpretation across all subplots

## Key Insights from Grid Analysis

### 1. Parameter Mapping Relationships
With correct parameter mappings identified, we now see six direct comparisons:
- **Perfect Match**: Brightness parameters are identical between Config and DM
- **Divergent Behavior**: PrecisionRenderingStrength (Config constant) vs LocalMappingStrength (DM decreasing)
- **Convergence Patterns**: Contrast, Saturation, and LocalContrast DM values converging toward Config levels

### 2. Convergence Patterns
Several DM parameters show convergence toward their Config counterparts:
- Contrast and Saturation parameters are converging exactly to Config values
- Local Contrast is converging toward Config level
- Brightness PR shows decreasing trend but not converging to Config level

### 3. Gain Parameter Coordination
All Config gain parameters follow identical decreasing patterns (127 → 42), suggesting coordinated adjustment of multiple gain controls.

### 4. Unique Behaviors
- **PrecisionRenderingStrength vs LocalMappingStrength**: Most interesting comparison showing Config constant while DM decreases significantly
- **UpMappingStrength**: Only parameter with increasing trend (DM-only)
- **Perfect Stability**: Brightness values maintain perfect Config-DM alignment

### 5. System Stability
Many parameters remain constant, indicating stable baseline settings while specific parameters are being dynamically adjusted to achieve convergence.

## Usage

### Viewing the Plot
Open `plots/all_parameters_comparison_grid.png` to see the comprehensive visualization.

### Regenerating the Plot
```bash
python grid_comparison_plotter.py
```

### Dependencies
- Python 3.x
- matplotlib
- numpy
- log_parser_plotter.py (for data parsing)

## Technical Details

- **Resolution**: 300 DPI for high-quality output
- **Format**: PNG with white background
- **Grid Optimization**: Automatic layout calculation based on parameter count
- **Frame Coverage**: All 300 frames from the log file
- **Color Accessibility**: High contrast blue/red color scheme

This comprehensive grid view enables quick identification of parameter relationships, trends, and convergence patterns across the entire TC Flash PQ parameter space.
