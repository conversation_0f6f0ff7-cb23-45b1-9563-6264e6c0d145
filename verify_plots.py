#!/usr/bin/env python3
"""
Simple verification script to check that plots were generated correctly
and display some sample data.
"""

import os
import matplotlib.pyplot as plt
import matplotlib.image as mpimg

def verify_plots():
    """Verify that all expected plots were generated."""
    plots_dir = "plots"
    
    if not os.path.exists(plots_dir):
        print("Error: plots directory not found!")
        return False
    
    # Expected files
    expected_config_files = [
        "config_intensity_level.png",
        "config_gain_PrecisionRenderingStrength.png", 
        "config_gain_DLocalContrast.png",
        "config_gain_DBrightness.png",
        "config_gainPos_DContrast.png",
        "config_gainPos_DSaturation.png",
        "config_gainNeg_DContrast.png",
        "config_gainNeg_DSaturation.png",
        "config_PrecisionRenderingStrength.png",
        "config_DBrightness.png",
        "config_DBrightness_PR_on.png",
        "config_DContrast.png",
        "config_DSaturation.png",
        "config_DLocalContrast.png",
        "config_overview.png"
    ]
    
    expected_dm_files = [
        "dm_LmOn.png",
        "dm_LocalMappingStrength.png",
        "dm_dBrightness.png",
        "dm_dBrightnessPRon.png",
        "dm_dContrast.png",
        "dm_dSaturation.png",
        "dm_dLocalContrast.png",
        "dm_UpMappingStrength.png",
        "dm_TMax.png",
        "dm_overview.png"
    ]
    
    all_expected = expected_config_files + expected_dm_files
    
    print("Verifying plot generation...")
    print(f"Expected {len(all_expected)} plot files")
    
    missing_files = []
    existing_files = []
    
    for filename in all_expected:
        filepath = os.path.join(plots_dir, filename)
        if os.path.exists(filepath):
            existing_files.append(filename)
            # Check file size to ensure it's not empty
            size = os.path.getsize(filepath)
            if size == 0:
                print(f"Warning: {filename} is empty!")
        else:
            missing_files.append(filename)
    
    print(f"Found {len(existing_files)} plot files")
    
    if missing_files:
        print(f"Missing files: {missing_files}")
        return False
    else:
        print("✓ All expected plot files found!")
        
        # Show file sizes
        print("\nFile sizes:")
        for filename in existing_files[:5]:  # Show first 5 as sample
            filepath = os.path.join(plots_dir, filename)
            size = os.path.getsize(filepath)
            print(f"  {filename}: {size:,} bytes")
        
        if len(existing_files) > 5:
            print(f"  ... and {len(existing_files) - 5} more files")
        
        return True

def show_sample_plot():
    """Display a sample plot to verify visual output."""
    sample_plot = "plots/config_gain_PrecisionRenderingStrength.png"
    
    if os.path.exists(sample_plot):
        print(f"\nDisplaying sample plot: {sample_plot}")
        
        # Load and display the image
        img = mpimg.imread(sample_plot)
        plt.figure(figsize=(10, 6))
        plt.imshow(img)
        plt.axis('off')
        plt.title("Sample Plot: Config gain_PrecisionRenderingStrength")
        plt.tight_layout()
        
        # Save a copy to verify matplotlib is working
        plt.savefig("sample_verification.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ Sample plot loaded and re-saved successfully!")
        print("  Created: sample_verification.png")
        
    else:
        print(f"Sample plot not found: {sample_plot}")

def main():
    """Main verification function."""
    print("Plot Verification Script")
    print("=" * 30)
    
    # Verify all plots exist
    success = verify_plots()
    
    if success:
        # Show a sample plot
        show_sample_plot()
        
        print("\n" + "=" * 30)
        print("Verification completed successfully!")
        print("\nTo view the plots:")
        print("1. Open the 'plots' directory")
        print("2. View individual parameter plots (*.png)")
        print("3. Check overview plots for multi-parameter views")
        print("\nKey plots to examine:")
        print("- config_overview.png (all varying Config parameters)")
        print("- dm_overview.png (all varying DM parameters)")
        print("- config_gain_*.png (shows decreasing trends)")
        print("- dm_LocalMappingStrength.png (shows decreasing trend)")
        print("- dm_UpMappingStrength.png (shows increasing trend)")
        
    else:
        print("\n❌ Verification failed!")
        print("Some expected plot files are missing.")
        print("Try running: python log_parser_plotter.py")

if __name__ == "__main__":
    main()
