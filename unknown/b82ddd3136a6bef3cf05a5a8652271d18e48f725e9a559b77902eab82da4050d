from matplotlib import backend_tools, widgets
from matplotlib.backend_bases import FigureCanvasBase
from matplotlib.figure import Figure

from collections.abc import Callable, Iterable
from typing import Any, TypeVar

class ToolEvent:
    name: str
    sender: Any
    tool: backend_tools.ToolBase
    data: Any
    def __init__(self, name, sender, tool, data: Any | None = ...) -> None: ...

class ToolTriggerEvent(ToolEvent):
    canvasevent: ToolEvent
    def __init__(
        self,
        name,
        sender,
        tool,
        canvasevent: ToolEvent | None = ...,
        data: Any | None = ...,
    ) -> None: ...

class ToolManagerMessageEvent:
    name: str
    sender: Any
    message: str
    def __init__(self, name: str, sender: Any, message: str) -> None: ...

class ToolManager:
    keypresslock: widgets.LockDraw
    messagelock: widgets.LockDraw
    def __init__(self, figure: Figure | None = ...) -> None: ...
    @property
    def canvas(self) -> FigureCanvasBase | None: ...
    @property
    def figure(self) -> Figure | None: ...
    @figure.setter
    def figure(self, figure: Figure) -> None: ...
    def set_figure(self, figure: Figure, update_tools: bool = ...) -> None: ...
    def toolmanager_connect(self, s: str, func: Callable[[ToolEvent], Any]) -> int: ...
    def toolmanager_disconnect(self, cid: int) -> None: ...
    def message_event(self, message: str, sender: Any | None = ...) -> None: ...
    @property
    def active_toggle(self) -> dict[str | None, list[str] | str]: ...
    def get_tool_keymap(self, name: str) -> list[str]: ...
    def update_keymap(self, name: str, key: str | Iterable[str]) -> None: ...
    def remove_tool(self, name: str) -> None: ...
    _T = TypeVar("_T", bound=backend_tools.ToolBase)
    def add_tool(self, name: str, tool: type[_T], *args, **kwargs) -> _T: ...
    def trigger_tool(
        self,
        name: str | backend_tools.ToolBase,
        sender: Any | None = ...,
        canvasevent: ToolEvent | None = ...,
        data: Any | None = ...,
    ) -> None: ...
    @property
    def tools(self) -> dict[str, backend_tools.ToolBase]: ...
    def get_tool(
        self, name: str | backend_tools.ToolBase, warn: bool = ...
    ) -> backend_tools.ToolBase | None: ...
