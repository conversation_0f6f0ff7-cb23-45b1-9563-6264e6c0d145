from collections.abc import Callable
from typing import Literal, overload

from numpy.typing import NDArray

__all__ = ["calculate_rms", "comparable_formats", "compare_images"]

def make_test_filename(fname: str, purpose: str) -> str: ...
def get_cache_dir() -> str: ...
def get_file_hash(path: str, block_size: int = ...) -> str: ...

converter: dict[str, Callable[[str, str], None]] = {}

def comparable_formats() -> list[str]: ...
def convert(filename: str, cache: bool) -> str: ...
def crop_to_same(
    actual_path: str, actual_image: NDArray, expected_path: str, expected_image: NDArray
) -> tuple[NDArray, NDArray]: ...
def calculate_rms(expected_image: NDArray, actual_image: NDArray) -> float: ...
@overload
def compare_images(
    expected: str, actual: str, tol: float, in_decorator: Literal[True]
) -> None | dict[str, float | str]: ...
@overload
def compare_images(
    expected: str, actual: str, tol: float, in_decorator: Literal[False]
) -> None | str: ...
@overload
def compare_images(
    expected: str, actual: str, tol: float, in_decorator: bool = ...
) -> None | str | dict[str, float | str]: ...
def save_diff_image(expected: str, actual: str, output: str) -> None: ...
