#!/usr/bin/env python3
"""
Comparison Plotter for TC Flash PQ Log Analysis

This script creates combined comparison plots that overlay corresponding parameters
from both "Config values" and "DM values" sections on the same graph.
"""

import re
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict
import os
from log_parser_plotter import parse_log_file

def identify_parameter_pairs(config_data, dm_data):
    """
    Identify corresponding parameters between Config and DM sections.
    
    Returns:
        dict: mapping of comparison_name -> {'config': config_param, 'dm': dm_param}
    """
    parameter_pairs = {}
    
    # Direct matches (case-insensitive, ignoring prefixes)
    direct_matches = [
        ('DBrightness', 'dBrightness'),
        ('DContrast', 'dContrast'), 
        ('DSaturation', 'dSaturation'),
        ('DLocalContrast', 'dLocalContrast'),
    ]
    
    for config_param, dm_param in direct_matches:
        if config_param in config_data and dm_param in dm_data:
            if "Brightness" in config_param:
                comparison_name = "Brightness_Comparison"
            elif "Contrast" in config_param and "Local" not in config_param:
                comparison_name = "Contrast_Comparison"
            elif "Saturation" in config_param:
                comparison_name = "Saturation_Comparison"
            elif "LocalContrast" in config_param:
                comparison_name = "LocalContrast_Comparison"
            else:
                comparison_name = f"{config_param}_vs_{dm_param}_Comparison"

            parameter_pairs[comparison_name] = {
                'config': config_param,
                'dm': dm_param
            }
    
    # Special case: DBrightness_PR_on vs dBrightnessPRon
    if 'DBrightness_PR_on' in config_data and 'dBrightnessPRon' in dm_data:
        parameter_pairs['BrightnessPR_Comparison'] = {
            'config': 'DBrightness_PR_on',
            'dm': 'dBrightnessPRon'
        }
    
    # Parameters that only exist in one section (will be plotted individually)
    config_only = {}
    dm_only = {}
    
    # Find Config-only parameters
    paired_config_params = {pair['config'] for pair in parameter_pairs.values()}
    for param in config_data.keys():
        if param not in paired_config_params:
            config_only[f"Config_{param}"] = {'config': param, 'dm': None}

    # Find DM-only parameters
    paired_dm_params = {pair['dm'] for pair in parameter_pairs.values()}
    for param in dm_data.keys():
        if param not in paired_dm_params:
            dm_only[f"DM_{param}"] = {'config': None, 'dm': param}
    
    return parameter_pairs, config_only, dm_only

def create_comparison_plots(config_data, dm_data, output_dir="plots"):
    """
    Create comparison plots overlaying Config and DM parameters.
    """
    # Create comparison subdirectory
    comparison_dir = os.path.join(output_dir, "comparisons")
    os.makedirs(comparison_dir, exist_ok=True)
    
    # Identify parameter pairs
    parameter_pairs, config_only, dm_only = identify_parameter_pairs(config_data, dm_data)
    
    print(f"Creating comparison plots...")
    print(f"Found {len(parameter_pairs)} parameter pairs for comparison")
    print(f"Found {len(config_only)} Config-only parameters")
    print(f"Found {len(dm_only)} DM-only parameters")
    
    # Set up matplotlib style
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (14, 8)
    plt.rcParams['font.size'] = 11
    
    # Create individual comparison plots for parameter pairs
    for comparison_name, params in parameter_pairs.items():
        create_individual_comparison_plot(
            config_data, dm_data, params, comparison_name, comparison_dir
        )
    
    # Create individual plots for Config-only parameters
    for param_name, params in config_only.items():
        create_single_parameter_plot(
            config_data, dm_data, params, param_name, comparison_dir
        )
    
    # Create individual plots for DM-only parameters
    for param_name, params in dm_only.items():
        create_single_parameter_plot(
            config_data, dm_data, params, param_name, comparison_dir
        )
    
    # Create overview comparison plot
    create_comparison_overview(
        config_data, dm_data, parameter_pairs, comparison_dir
    )
    
    return len(parameter_pairs) + len(config_only) + len(dm_only)

def create_individual_comparison_plot(config_data, dm_data, params, comparison_name, output_dir):
    """
    Create an individual comparison plot for a parameter pair.
    """
    config_param = params['config']
    dm_param = params['dm']
    
    config_values = config_data[config_param]
    dm_values = dm_data[dm_param]
    
    frames = list(range(1, len(config_values) + 1))
    
    plt.figure(figsize=(14, 8))
    
    # Plot Config values in blue
    plt.plot(frames, config_values, 
             color='blue', linewidth=2.5, marker='o', markersize=4,
             label=f'Config: {config_param}', alpha=0.8)
    
    # Plot DM values in red
    plt.plot(frames, dm_values,
             color='red', linewidth=2.5, marker='s', markersize=4, 
             linestyle='--', label=f'DM: {dm_param}', alpha=0.8)
    
    # Formatting
    plt.title(f'Parameter Comparison: {comparison_name}', fontsize=16, fontweight='bold')
    plt.xlabel('Frame Number', fontsize=13)
    plt.ylabel('Parameter Value', fontsize=13)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12, loc='best')
    
    # Add statistics
    config_mean = np.mean(config_values)
    dm_mean = np.mean(dm_values)
    
    # Add mean lines
    plt.axhline(y=config_mean, color='blue', linestyle=':', alpha=0.6, 
               label=f'Config Mean: {config_mean:.1f}')
    plt.axhline(y=dm_mean, color='red', linestyle=':', alpha=0.6,
               label=f'DM Mean: {dm_mean:.1f}')
    
    # Update legend to include means
    plt.legend(fontsize=11, loc='best')
    
    # Add text box with statistics
    stats_text = f'Config Range: [{min(config_values)}-{max(config_values)}]\n'
    stats_text += f'DM Range: [{min(dm_values)}-{max(dm_values)}]\n'
    stats_text += f'Config Varies: {len(set(config_values)) > 1}\n'
    stats_text += f'DM Varies: {len(set(dm_values)) > 1}'
    
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # Save plot
    safe_name = re.sub(r'[^\w\-_]', '_', comparison_name)
    filename = f"comparison_{safe_name}.png"
    filepath = os.path.join(output_dir, filename)
    
    plt.tight_layout()
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"  Saved: {filename}")

def create_single_parameter_plot(config_data, dm_data, params, param_name, output_dir):
    """
    Create a plot for parameters that exist in only one section.
    """
    config_param = params['config']
    dm_param = params['dm']
    
    if config_param:
        values = config_data[config_param]
        color = 'blue'
        section = 'Config'
        actual_param = config_param
    else:
        values = dm_data[dm_param]
        color = 'red'
        section = 'DM'
        actual_param = dm_param
    
    frames = list(range(1, len(values) + 1))
    
    plt.figure(figsize=(14, 8))
    
    # Plot the parameter
    plt.plot(frames, values,
             color=color, linewidth=2.5, marker='o', markersize=4,
             label=f'{section}: {actual_param}', alpha=0.8)
    
    # Formatting
    plt.title(f'{section}-Only Parameter: {actual_param}', fontsize=16, fontweight='bold')
    plt.xlabel('Frame Number', fontsize=13)
    plt.ylabel('Parameter Value', fontsize=13)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12, loc='best')
    
    # Add statistics
    mean_val = np.mean(values)
    plt.axhline(y=mean_val, color=color, linestyle=':', alpha=0.6,
               label=f'Mean: {mean_val:.1f}')
    
    # Add trend line if parameter varies
    if len(set(values)) > 1:
        z = np.polyfit(frames, values, 1)
        p = np.poly1d(z)
        plt.plot(frames, p(frames), color=color, linestyle='-.', alpha=0.5, linewidth=2,
                label=f'Trend: {"↗" if z[0] > 0 else "↘"}')
    
    plt.legend(fontsize=11, loc='best')
    
    # Add statistics text box
    stats_text = f'Range: [{min(values)}-{max(values)}]\n'
    stats_text += f'Varies: {len(set(values)) > 1}\n'
    stats_text += f'Std Dev: {np.std(values):.1f}'
    
    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue' if section == 'Config' else 'lightcoral', alpha=0.8))
    
    # Save plot
    safe_name = re.sub(r'[^\w\-_]', '_', param_name)
    filename = f"single_{safe_name}.png"
    filepath = os.path.join(output_dir, filename)
    
    plt.tight_layout()
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"  Saved: {filename}")

def create_comparison_overview(config_data, dm_data, parameter_pairs, output_dir):
    """
    Create an overview plot showing multiple parameter comparisons.
    """
    if not parameter_pairs:
        print("  No parameter pairs found for overview plot")
        return
    
    # Calculate subplot layout
    n_pairs = len(parameter_pairs)
    n_cols = min(2, n_pairs)
    n_rows = (n_pairs + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(16, 6 * n_rows))
    fig.suptitle('Parameter Comparison Overview: Config vs DM Values', 
                 fontsize=18, fontweight='bold')
    
    # Handle single subplot case
    if n_pairs == 1:
        axes = [axes]
    elif n_rows == 1 and n_cols > 1:
        axes = axes
    elif n_rows > 1:
        axes = axes.flatten()
    else:
        axes = [axes]
    
    for i, (comparison_name, params) in enumerate(parameter_pairs.items()):
        config_param = params['config']
        dm_param = params['dm']
        
        config_values = config_data[config_param]
        dm_values = dm_data[dm_param]
        frames = list(range(1, len(config_values) + 1))
        
        ax = axes[i]
        
        # Plot both parameters
        ax.plot(frames, config_values, color='blue', linewidth=2, marker='o', 
                markersize=3, label=f'Config: {config_param}', alpha=0.8)
        ax.plot(frames, dm_values, color='red', linewidth=2, marker='s',
                markersize=3, linestyle='--', label=f'DM: {dm_param}', alpha=0.8)
        
        ax.set_title(comparison_name.replace('_', ' '), fontsize=12, fontweight='bold')
        ax.set_xlabel('Frame')
        ax.set_ylabel('Value')
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=9, loc='best')
    
    # Hide unused subplots
    for i in range(n_pairs, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    
    # Save overview plot
    filepath = os.path.join(output_dir, "comparison_overview.png")
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"  Saved: comparison_overview.png")

def main():
    """
    Main function to create comparison plots.
    """
    log_filename = "TC_Flash_first_few_frames_pq_log.txt"
    
    print("TC Flash PQ Log Comparison Plotter")
    print("="*45)
    
    # Check if log file exists
    if not os.path.exists(log_filename):
        print(f"Error: Log file '{log_filename}' not found!")
        return
    
    print(f"Parsing log file: {log_filename}")
    
    # Parse the log file
    config_data, dm_data = parse_log_file(log_filename)
    
    # Create comparison plots
    total_plots = create_comparison_plots(config_data, dm_data)
    
    print(f"\nComparison plotting complete!")
    print(f"Generated {total_plots} comparison plots plus 1 overview plot.")
    print(f"Check the 'plots/comparisons' directory for output files.")
    
    print(f"\n" + "="*50)
    print("COMPARISON SUMMARY")
    print("="*50)
    print("Key comparisons created:")
    print("  - Brightness: DBrightness vs dBrightness")
    print("  - Contrast: DContrast vs dContrast")
    print("  - Saturation: DSaturation vs dSaturation")
    print("  - Local Contrast: DLocalContrast vs dLocalContrast")
    print("  - Brightness PR: DBrightness_PR_on vs dBrightnessPRon")
    print("\nColor coding:")
    print("  - Blue lines: Config values")
    print("  - Red lines: DM values")
    print("  - Solid lines: Config parameters")
    print("  - Dashed lines: DM parameters")

if __name__ == "__main__":
    main()
