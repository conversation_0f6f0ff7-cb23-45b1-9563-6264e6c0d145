# TC Flash PQ Log Analysis

This project contains a Python script to parse and visualize the TC Flash PQ log data from `TC_Flash_first_few_frames_pq_log.txt`.

## Overview

The log file contains 300 frames of data with two main sections per frame:
- **Config values**: 14 parameters including gain settings and rendering parameters
- **DM values**: 9 parameters including mapping strengths and contrast settings

## Generated Plots

The script generates the following types of visualizations:

### Individual Parameter Plots
Each parameter gets its own line plot showing progression over 300 frames:

#### Config Values (14 plots):
- `config_intensity_level.png` - Constant at 10 (no variation)
- `config_gain_PrecisionRenderingStrength.png` - Decreases from 127 to 42
- `config_gain_DLocalContrast.png` - Decreases from 127 to 42  
- `config_gain_DBrightness.png` - Decreases from 127 to 42
- `config_gainPos_DContrast.png` - Decreases from 127 to 42
- `config_gainPos_DSaturation.png` - Decreases from 127 to 42
- `config_gainNeg_DContrast.png` - Decreases from 127 to 42
- `config_gainNeg_DSaturation.png` - Decreases from 127 to 42
- `config_PrecisionRenderingStrength.png` - Constant at 24576
- `config_DBrightness.png` - Constant at 9830
- `config_DBrightness_PR_on.png` - Constant at 11469
- `config_DContrast.png` - Constant at 3277
- `config_DSaturation.png` - Constant at 3277
- `config_DLocalContrast.png` - Constant at 4915

#### DM Values (9 plots):
- `dm_LmOn.png` - Constant at 1
- `dm_LocalMappingStrength.png` - Decreases from 16319 to 5397
- `dm_dBrightness.png` - Constant at 9830
- `dm_dBrightnessPRon.png` - Decreases from 16319 to 5397
- `dm_dContrast.png` - Decreases from 8224 to 2719
- `dm_dSaturation.png` - Decreases from 8224 to 2719
- `dm_dLocalContrast.png` - Decreases from 16319 to 5397
- `dm_UpMappingStrength.png` - Increases from 32960 to 48676
- `dm_TMax.png` - Constant at 208404480

### Overview Plots
- `config_overview.png` - Multi-subplot view of all varying Config parameters
- `dm_overview.png` - Multi-subplot view of all varying DM parameters

## Key Observations

### Trending Parameters
Several parameters show clear linear trends over the 300 frames:

**Decreasing trends:**
- All gain parameters (Config): 127 → 42 (linear decrease)
- LocalMappingStrength (DM): 16319 → 5397
- dBrightnessPRon (DM): 16319 → 5397  
- dContrast/dSaturation (DM): 8224 → 2719
- dLocalContrast (DM): 16319 → 5397

**Increasing trends:**
- UpMappingStrength (DM): 32960 → 48676

**Constant parameters:**
- intensity_level, PrecisionRenderingStrength, DBrightness, DBrightness_PR_on, DContrast, DSaturation, DLocalContrast (Config)
- LmOn, dBrightness, TMax (DM)

## Usage

Run the script to regenerate all plots:

```bash
python log_parser_plotter.py
```

### Requirements
- Python 3.x
- matplotlib
- numpy

Install dependencies:
```bash
pip install matplotlib numpy
```

## Script Features

- **Automatic parsing**: Handles both Config and DM value formats
- **Scale handling**: Extracts numeric values and ignores scale information
- **Trend analysis**: Adds trend lines to overview plots
- **Statistical info**: Shows mean values on individual plots
- **Clean output**: Organized file naming and directory structure
- **Error handling**: Robust parsing with detailed summary output

## File Structure

```
├── TC_Flash_first_few_frames_pq_log.txt  # Input log file
├── log_parser_plotter.py                 # Main parsing script
├── README.md                              # This documentation
└── plots/                                 # Generated visualizations
    ├── config_*.png                       # Individual Config parameter plots
    ├── dm_*.png                           # Individual DM parameter plots
    ├── config_overview.png                # Config parameters overview
    └── dm_overview.png                    # DM parameters overview
```

The plots clearly show the parameter evolution over the 300 frames, with most gain-related parameters following a steady decreasing trend while mapping strength parameters show various patterns of change.
